'use client';

import * as React from 'react';
import type { Value } from '@udecode/plate';

import { createBasicElementsPlugin } from '@udecode/plate-basic-elements';
import { createBasicMarksPlugin } from '@udecode/plate-basic-marks';
import { createListPlugin } from '@udecode/plate-list';
import { createTablePlugin } from '@udecode/plate-table';
import { createLinkPlugin } from '@udecode/plate-link';
import {
  type PlateElementProps,
  type PlateLeafProps,
  Plate,
  PlateLeaf,
  createPlateEditor,
  PlateContent,
  useEditorRef,
} from '@udecode/plate';

// Define the default initial value for the editor
const defaultInitialValue: Value = [
  { type: 'p', children: [{ text: '' }] },
];

// Define the props for our RichTextEditor component
export interface RichTextEditorProps {
  name?: string;
  value?: Value;
  onChange?: (value: Value) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  readOnly?: boolean;
  label?: string;
  error?: string;
  description?: string;
}

// Create a toolbar button component
const ToolbarButton = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ className, ...props }, ref) => {
  return (
    <button
      ref={ref}
      className={`rounded border border-gray-300 bg-white px-2 py-1 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${className || ''}`}
      {...props}
    />
  );
});
ToolbarButton.displayName = 'ToolbarButton';

// Create a mark toolbar button component
const MarkToolbarButton = ({
  nodeType,
  children,
  tooltip,
  ...props
}: {
  nodeType: string;
  children: React.ReactNode;
  tooltip?: string;
} & React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  const editor = usePlateEditor();

  return (
    <ToolbarButton
      title={tooltip}
      onClick={() => {
        editor.tf.toggleMark(nodeType);
      }}
      {...props}
    >
      {children}
    </ToolbarButton>
  );
};

// Create a block toolbar button component
const BlockToolbarButton = ({
  nodeType,
  children,
  tooltip,
  ...props
}: {
  nodeType: string;
  children: React.ReactNode;
  tooltip?: string;
} & React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  const editor = usePlateEditor();

  return (
    <ToolbarButton
      title={tooltip}
      onClick={() => {
        editor.tf.toggleBlock(nodeType);
      }}
      {...props}
    >
      {children}
    </ToolbarButton>
  );
};

// Create a list toolbar button component
const ListToolbarButton = ({
  nodeType,
  children,
  tooltip,
  ...props
}: {
  nodeType: 'ul' | 'ol';
  children: React.ReactNode;
  tooltip?: string;
} & React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  const editor = usePlateEditor();

  return (
    <ToolbarButton
      title={tooltip}
      onClick={() => {
        if (nodeType === 'ul') {
          editor.tf.toggleList('ul');
        } else {
          editor.tf.toggleList('ol');
        }
      }}
      {...props}
    >
      {children}
    </ToolbarButton>
  );
};

// Create the main RichTextEditor component
export function RichTextEditor({
  name,
  value,
  onChange,
  placeholder = 'Type here...',
  className = '',
  minHeight = '150px',
  readOnly = false,
  label,
  error,
  description,
}: RichTextEditorProps) {
  // Create the editor instance
  const editor = usePlateEditor({
    plugins: [
      createBasicElementsPlugin(),
      createBasicMarksPlugin(),
      createListPlugin(),
      createTablePlugin(),
      createLinkPlugin(),
    ],
    value: value || defaultInitialValue,
    readOnly,
  });

  // Define the components for rendering elements and marks
  const components = {
    // Element components
    p: (props: PlateElementProps) => <p {...props.attributes} className="my-1">{props.children}</p>,
    h1: (props: PlateElementProps) => <h1 {...props.attributes} className="text-2xl font-bold my-2">{props.children}</h1>,
    h2: (props: PlateElementProps) => <h2 {...props.attributes} className="text-xl font-bold my-2">{props.children}</h2>,
    h3: (props: PlateElementProps) => <h3 {...props.attributes} className="text-lg font-bold my-2">{props.children}</h3>,
    blockquote: (props: PlateElementProps) => (
      <blockquote {...props.attributes} className="border-l-4 border-gray-300 pl-4 italic my-2">
        {props.children}
      </blockquote>
    ),
    // Mark components
    bold: (props: PlateLeafProps) => <PlateLeaf {...props} as="strong" />,
    italic: (props: PlateLeafProps) => <PlateLeaf {...props} as="em" />,
    underline: (props: PlateLeafProps) => <PlateLeaf {...props} as="u" />,
  };

  return (
    <div className={`w-full ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <Plate
        editor={editor}
        onChange={({ value }) => {
          if (onChange) {
            onChange(value);
          }
          // If name is provided, update a hidden input for form submission
          if (name) {
            const hiddenInput = document.querySelector(`input[name="${name}"]`) as HTMLInputElement;
            if (hiddenInput) {
              hiddenInput.value = JSON.stringify(value);
            }
          }
        }}
        initialValue={value || defaultInitialValue}
        components={components}
      >
        {!readOnly && (
          <div className="flex flex-wrap gap-1 p-1 border border-gray-300 rounded-t-md bg-gray-50">
            <MarkToolbarButton nodeType="bold" tooltip="Bold (⌘+B)">B</MarkToolbarButton>
            <MarkToolbarButton nodeType="italic" tooltip="Italic (⌘+I)">I</MarkToolbarButton>
            <MarkToolbarButton nodeType="underline" tooltip="Underline (⌘+U)">U</MarkToolbarButton>
            <div className="w-px h-6 mx-1 bg-gray-300" />
            <BlockToolbarButton nodeType="h1" tooltip="Heading 1">H1</BlockToolbarButton>
            <BlockToolbarButton nodeType="h2" tooltip="Heading 2">H2</BlockToolbarButton>
            <BlockToolbarButton nodeType="h3" tooltip="Heading 3">H3</BlockToolbarButton>
            <BlockToolbarButton nodeType="blockquote" tooltip="Quote">""</BlockToolbarButton>
            <div className="w-px h-6 mx-1 bg-gray-300" />
            <ListToolbarButton nodeType="ul" tooltip="Bullet List">• List</ListToolbarButton>
            <ListToolbarButton nodeType="ol" tooltip="Numbered List">1. List</ListToolbarButton>
          </div>
        )}
        <div 
          className={`border ${readOnly ? 'border-gray-200' : 'border-gray-300'} ${!readOnly && 'border-t-0'} rounded-b-md p-2 ${readOnly ? 'bg-gray-50' : 'bg-white'}`}
          style={{ minHeight }}
        >
          <PlateContent 
            placeholder={placeholder}
            className="outline-none"
          />
        </div>
        {name && (
          <input type="hidden" name={name} value={JSON.stringify(value || defaultInitialValue)} />
        )}
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
        {description && (
          <p className="mt-1 text-sm text-gray-500">{description}</p>
        )}
      </Plate>
    </div>
  );
}

// Create a read-only version of the editor for displaying content
export function RichTextDisplay({
  value,
  className = '',
  minHeight = 'auto',
}: {
  value: Value;
  className?: string;
  minHeight?: string;
}) {
  return (
    <RichTextEditor
      value={value}
      readOnly={true}
      className={className}
      minHeight={minHeight}
    />
  );
}

// Helper function to convert a string to a Plate Value
export function stringToPlateValue(text: string): Value {
  if (!text) return defaultInitialValue;
  
  // Split the text by newlines and create paragraphs
  return text.split('\n').map(line => ({
    type: 'p',
    children: [{ text: line }],
  }));
}

// Helper function to convert a Plate Value to a string
export function plateValueToString(value: Value): string {
  if (!value || !Array.isArray(value) || value.length === 0) return '';
  
  // Extract text from each node and join with newlines
  return value
    .map(node => {
      if (!node.children) return '';
      return node.children
        .map(child => 'text' in child ? child.text : '')
        .join('');
    })
    .join('\n');
}