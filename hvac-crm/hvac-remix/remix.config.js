/** @type {import('@remix-run/dev').AppConfig} */
export default {
  cacheDirectory: "./node_modules/.cache/remix",
  ignoredRouteFiles: ["**/.*", "**/*.test.{ts,tsx}", "**/routes/**/*.server.{ts,tsx}"],
  serverModuleFormat: "esm",
  serverPlatform: "node",
  tailwind: true,
  postcss: true,
  watchPaths: ["./tailwind.config.ts"],

  // Enable all future features for better performance and developer experience
  future: {
    v3_fetcherPersist: true,
    v3_lazyRouteDiscovery: true,
    v3_relativeSplatPath: true,
    v3_singleFetch: true,
    v3_throwAbortReason: true,
    v3_meta: true,
    v3_normalizeFormMethod: true,
    v3_errorBoundary: true,
  },

  // Bundle dependencies that might cause issues in production
  serverDependenciesToBundle: [
    /^@udecode\/.*/,
    /^plate.*/,
    "recharts",
    "react-day-picker",
    /^marked.*/,
    /^lodash.*/,
    /^date-fns.*/,
    /^gsap.*/,
  ],

  // Optimize asset loading
  assetsBuildDirectory: "public/build",
  publicPath: "/build/",

  // Enable source maps in production for better error tracking
  sourcemap: true,

  // Define routes with preloading for better performance
  routes: async (defineRoutes) => {
    return defineRoutes((route) => {
      // High-traffic routes that should be preloaded for better performance
      route("/", "routes/index.tsx", { preload: true });
      route("/dashboard", "routes/dashboard.tsx", { preload: true });
      route("/login", "routes/login.tsx", { preload: true });
      route("/customers", "routes/customers._index.tsx", { preload: true });
      route("/calendar", "routes/calendar.tsx", { preload: true });
      route("/service-orders", "routes/service-orders.tsx", { preload: true });

      // Admin routes (lower priority for preloading)
      route("/admin", "routes/admin.tsx");
      route("/settings", "routes/settings.tsx");
    });
  }
};