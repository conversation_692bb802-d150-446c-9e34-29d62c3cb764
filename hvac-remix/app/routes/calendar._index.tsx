import * as React from "react";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams, Link } from "@remix-run/react";
import { requireUserId } from "~/session.server";
import { getCalendarEntriesByDateRange, type CalendarEntry } from "~/services/calendar.service";
import { getServiceOrders } from "~/services/service-order.service";
import { Button } from "~/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";

// Helper functions for date manipulation
const getDaysInMonth = (year: number, month: number) => new Date(year, month + 1, 0).getDate();
const getFirstDayOfMonth = (year: number, month: number) => new Date(year, month, 1).getDay();
const formatDate = (date: Date) => date.toISOString().split('T')[0];
const addDays = (date: Date, days: number) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Get date parameters from URL or use current date
  const today = new Date();
  const yearParam = url.searchParams.get("year");
  const monthParam = url.searchParams.get("month");
  const dayParam = url.searchParams.get("day");

  const year = yearParam ? parseInt(yearParam, 10) : today.getFullYear();
  const month = monthParam ? parseInt(monthParam, 10) : today.getMonth();
  const day = dayParam ? parseInt(dayParam, 10) : today.getDate();

  const currentDate = new Date(year, month, day);

  // Calculate start and end dates for different views
  const monthStart = new Date(year, month, 1);
  const monthEnd = new Date(year, month + 1, 0);

  const weekStart = new Date(currentDate);
  weekStart.setDate(currentDate.getDate() - currentDate.getDay());
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);

  const dayStart = new Date(year, month, day);
  const dayEnd = new Date(year, month, day, 23, 59, 59);

  // Get view type from URL or default to month
  const viewType = url.searchParams.get("view") || "month";

  // Determine date range based on view type
  let startDate, endDate;
  if (viewType === "month") {
    startDate = formatDate(monthStart);
    endDate = formatDate(monthEnd);
  } else if (viewType === "week") {
    startDate = formatDate(weekStart);
    endDate = formatDate(weekEnd);
  } else {
    startDate = formatDate(dayStart);
    endDate = formatDate(dayEnd);
  }

  // Get calendar entries for the date range
  const calendarResponse = await getCalendarEntriesByDateRange(userId, startDate, endDate);

  // Get service orders with scheduled dates in the range
  const serviceOrdersResponse = await getServiceOrders(
    userId,
    { pageSize: 100 },
    { dateFrom: startDate, dateTo: endDate }
  );

  return json({
    calendarEntries: calendarResponse.data || [],
    serviceOrders: serviceOrdersResponse.data || [],
    currentDate: {
      year,
      month,
      day,
      formatted: formatDate(currentDate)
    },
    viewType,
    dateRange: {
      start: startDate,
      end: endDate
    }
  });
}

export default function CalendarPage() {
  const {
    calendarEntries,
    serviceOrders,
    currentDate,
    viewType,
    dateRange
  } = useLoaderData<typeof loader>();

  const [searchParams, setSearchParams] = useSearchParams();

  // Handle view type change
  const handleViewChange = (view: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("view", view);
    setSearchParams(newParams);
  };

  // Handle navigation (prev, next, today)
  const handleNavigation = (direction: "prev" | "next" | "today") => {
    const newParams = new URLSearchParams(searchParams);
    const currentViewType = searchParams.get("view") || "month";

    const today = new Date();
    let year = currentDate.year;
    let month = currentDate.month;
    let day = currentDate.day;

    if (direction === "today") {
      year = today.getFullYear();
      month = today.getMonth();
      day = today.getDate();
    } else {
      if (currentViewType === "month") {
        month = direction === "next" ? month + 1 : month - 1;
        if (month > 11) {
          month = 0;
          year++;
        } else if (month < 0) {
          month = 11;
          year--;
        }
      } else if (currentViewType === "week") {
        const currentDateObj = new Date(year, month, day);
        const newDate = direction === "next"
          ? addDays(currentDateObj, 7)
          : addDays(currentDateObj, -7);
        year = newDate.getFullYear();
        month = newDate.getMonth();
        day = newDate.getDate();
      } else {
        const currentDateObj = new Date(year, month, day);
        const newDate = direction === "next"
          ? addDays(currentDateObj, 1)
          : addDays(currentDateObj, -1);
        year = newDate.getFullYear();
        month = newDate.getMonth();
        day = newDate.getDate();
      }
    }

    newParams.set("year", year.toString());
    newParams.set("month", month.toString());
    newParams.set("day", day.toString());
    setSearchParams(newParams);
  };

  // Combine calendar entries and service orders for display
  const events = [
    ...calendarEntries.map(entry => ({
      id: entry.id,
      title: entry.title,
      start: new Date(entry.startTime),
      end: new Date(entry.endTime),
      isAllDay: entry.isAllDay,
      type: 'calendar',
      color: 'bg-blue-100 text-blue-800'
    })),
    ...serviceOrders.map(order => ({
      id: order.id,
      title: order.title,
      start: order.scheduledDate ? new Date(order.scheduledDate) : null,
      end: order.scheduledDate ? new Date(order.scheduledDate) : null,
      isAllDay: false,
      type: 'service-order',
      orderType: order.type,
      color: getServiceOrderTypeColor(order.type)
    })).filter(order => order.start !== null)
  ];

  // Format month name for display
  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Calendar</h1>
        <Link to="/calendar/new">
          <Button>Add Event</Button>
        </Link>
      </div>

      {/* Calendar Navigation */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => handleNavigation("prev")}>
            Previous
          </Button>
          <Button variant="outline" onClick={() => handleNavigation("today")}>
            Today
          </Button>
          <Button variant="outline" onClick={() => handleNavigation("next")}>
            Next
          </Button>
        </div>

        <h2 className="text-xl font-semibold">
          {viewType === "day"
            ? `${monthNames[currentDate.month]} ${currentDate.day}, ${currentDate.year}`
            : viewType === "week"
              ? `Week of ${monthNames[new Date(dateRange.start).getMonth()]} ${new Date(dateRange.start).getDate()}`
              : `${monthNames[currentDate.month]} ${currentDate.year}`
          }
        </h2>

        <Tabs value={viewType} onValueChange={handleViewChange}>
          <TabsList>
            <TabsTrigger value="month">Month</TabsTrigger>
            <TabsTrigger value="week">Week</TabsTrigger>
            <TabsTrigger value="day">Day</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Calendar Legend */}
      <div className="flex flex-wrap gap-4 mb-4 p-2 bg-gray-50 rounded-md">
        <div className="text-sm font-medium">Legend:</div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-blue-100 border border-blue-800"></div>
          <span className="text-sm">Service</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-green-100 border border-green-800"></div>
          <span className="text-sm">Installation</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-yellow-100 border border-yellow-800"></div>
          <span className="text-sm">Inspection</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-full bg-blue-100 border border-blue-800 opacity-50"></div>
          <span className="text-sm">Calendar Event</span>
        </div>
      </div>

      {/* Calendar Views */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {viewType === "month" && (
          <MonthView
            year={currentDate.year}
            month={currentDate.month}
            events={events}
          />
        )}

        {viewType === "week" && (
          <WeekView
            startDate={new Date(dateRange.start)}
            events={events}
          />
        )}

        {viewType === "day" && (
          <DayView
            date={new Date(currentDate.year, currentDate.month, currentDate.day)}
            events={events}
          />
        )}
      </div>
    </div>
  );
}

// Month View Component
function MonthView({ year, month, events }: {
  year: number,
  month: number,
  events: any[]
}) {
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayOfMonth = getFirstDayOfMonth(year, month);
  const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  // Create calendar grid
  const days = [];
  for (let i = 0; i < firstDayOfMonth; i++) {
    days.push(null); // Empty cells for days before the 1st of the month
  }

  for (let i = 1; i <= daysInMonth; i++) {
    days.push(i);
  }

  return (
    <div>
      {/* Weekday headers */}
      <div className="grid grid-cols-7 border-b">
        {weekdays.map(day => (
          <div key={day} className="p-2 text-center font-semibold">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 auto-rows-fr">
        {days.map((day, index) => {
          if (day === null) {
            return <div key={`empty-${index}`} className="border p-1 bg-gray-50"></div>;
          }

          const date = new Date(year, month, day);
          const dayEvents = events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate.getDate() === day &&
                   eventDate.getMonth() === month &&
                   eventDate.getFullYear() === year;
          });

          const isToday = new Date().toDateString() === date.toDateString();

          return (
            <div
              key={`day-${day}`}
              className={`border p-1 min-h-[100px] ${isToday ? 'bg-blue-50' : ''}`}
            >
              <div className={`text-right ${isToday ? 'font-bold text-blue-600' : ''}`}>
                {day}
              </div>
              <div className="mt-1 space-y-1 max-h-[80px] overflow-y-auto">
                {dayEvents.slice(0, 3).map(event => (
                  <Link
                    key={`${event.type}-${event.id}`}
                    to={event.type === 'calendar' ? `/calendar/${event.id}` : `/service-orders/${event.id}`}
                    className={`block text-xs p-1 rounded truncate ${event.color}`}
                  >
                    {event.title}
                  </Link>
                ))}
                {dayEvents.length > 3 && (
                  <div className="text-xs text-gray-500 p-1">
                    +{dayEvents.length - 3} more
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Week View Component
function WeekView({ startDate, events }: {
  startDate: Date,
  events: any[]
}) {
  const weekdays = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
  const hours = Array.from({ length: 24 }, (_, i) => i);

  return (
    <div className="flex flex-col">
      {/* Weekday headers */}
      <div className="grid grid-cols-8 border-b">
        <div className="p-2 border-r"></div>
        {weekdays.map((day, index) => {
          const date = addDays(startDate, index);
          const isToday = new Date().toDateString() === date.toDateString();

          return (
            <div
              key={day}
              className={`p-2 text-center font-semibold ${isToday ? 'bg-blue-50' : ''}`}
            >
              <div>{day}</div>
              <div className={`text-sm ${isToday ? 'text-blue-600' : 'text-gray-500'}`}>
                {date.getDate()}
              </div>
            </div>
          );
        })}
      </div>

      {/* Time grid */}
      <div className="flex-1 overflow-y-auto" style={{ height: '600px' }}>
        {hours.map(hour => (
          <div key={`hour-${hour}`} className="grid grid-cols-8 border-b">
            <div className="p-1 text-xs text-right pr-2 border-r">
              {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
            </div>

            {weekdays.map((_, dayIndex) => {
              const date = addDays(startDate, dayIndex);
              date.setHours(hour);

              const hourEvents = events.filter(event => {
                const eventDate = new Date(event.start);
                return eventDate.getDate() === date.getDate() &&
                       eventDate.getMonth() === date.getMonth() &&
                       eventDate.getFullYear() === date.getFullYear() &&
                       eventDate.getHours() === hour;
              });

              return (
                <div key={`day-${dayIndex}-hour-${hour}`} className="border-r p-1 min-h-[40px]">
                  {hourEvents.map(event => (
                    <Link
                      key={`${event.type}-${event.id}`}
                      to={event.type === 'calendar' ? `/calendar/${event.id}` : `/service-orders/${event.id}`}
                      className={`block text-xs p-1 rounded truncate mb-1 ${event.color}`}
                    >
                      {event.title}
                    </Link>
                  ))}
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
}

// Day View Component
function DayView({ date, events }: {
  date: Date,
  events: any[]
}) {
  const hours = Array.from({ length: 24 }, (_, i) => i);

  return (
    <div className="flex flex-col">
      {/* Day header */}
      <div className="p-4 text-center font-semibold border-b">
        <div className="text-lg">
          {date.toLocaleDateString(undefined, {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </div>
      </div>

      {/* All-day events */}
      <div className="border-b p-2">
        <div className="font-semibold mb-2">All-day Events</div>
        {events.filter(event => event.isAllDay).map(event => (
          <Link
            key={`${event.type}-${event.id}`}
            to={event.type === 'calendar' ? `/calendar/${event.id}` : `/service-orders/${event.id}`}
            className={`block p-2 rounded mb-1 ${event.color}`}
          >
            {event.title}
          </Link>
        ))}
      </div>

      {/* Time grid */}
      <div className="flex-1 overflow-y-auto" style={{ height: '600px' }}>
        {hours.map(hour => {
          const currentDate = new Date(date);
          currentDate.setHours(hour);

          const hourEvents = events.filter(event => {
            if (event.isAllDay) return false;
            const eventDate = new Date(event.start);
            return eventDate.getDate() === currentDate.getDate() &&
                   eventDate.getMonth() === currentDate.getMonth() &&
                   eventDate.getFullYear() === currentDate.getFullYear() &&
                   eventDate.getHours() === hour;
          });

          return (
            <div key={`hour-${hour}`} className="grid grid-cols-12 border-b">
              <div className="col-span-1 p-2 text-right border-r">
                {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
              </div>

              <div className="col-span-11 p-2 min-h-[60px]">
                {hourEvents.map(event => (
                  <Link
                    key={`${event.type}-${event.id}`}
                    to={event.type === 'calendar' ? `/calendar/${event.id}` : `/service-orders/${event.id}`}
                    className={`block p-2 rounded mb-1 ${event.color}`}
                  >
                    <div className="font-semibold">{event.title}</div>
                    <div className="text-xs">
                      {new Date(event.start).toLocaleTimeString(undefined, {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                      {event.end && ` - ${new Date(event.end).toLocaleTimeString(undefined, {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}`}
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Helper function to get status color
function getStatusColor(status: string) {
  switch (status.toUpperCase()) {
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "IN_PROGRESS":
      return "bg-blue-100 text-blue-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function to get service order type color
function getServiceOrderTypeColor(type: string) {
  switch (type?.toUpperCase()) {
    case "SERVICE":
      return "bg-blue-100 text-blue-800";
    case "INSTALLATION":
      return "bg-green-100 text-green-800";
    case "INSPECTION":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}
