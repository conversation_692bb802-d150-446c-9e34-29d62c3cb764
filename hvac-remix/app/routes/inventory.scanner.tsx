import * as React from "react";
import { json, type ActionFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useSubmit } from "@remix-run/react";
import { useState, useRef, useEffect } from "react";
import { ArchiveBoxIcon, QrCodeIcon, ArrowDownTrayIcon, CameraIcon } from "@heroicons/react/24/outline";
import { requireUserId } from "~/session.server";
import { prisma } from "~/db.server";

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get("action") as string;

  switch (action) {
    case "lookupBarcode": {
      const barcode = formData.get("barcode") as string;

      if (!barcode) {
        return json({ success: false, error: "No barcode provided" });
      }

      // In a real implementation, we would look up the barcode in the database
      // Here we're simulating a successful lookup with mock data
      try {
        // Look up part by SKU or part number
        const part = await prisma.inventoryPart.findFirst({
          where: {
            OR: [
              { sku: barcode },
              { partNumber: barcode }
            ]
          },
          include: {
            supplier: true,
            inventoryLocations: {
              include: {
                location: true
              }
            }
          }
        });

        if (!part) {
          // For demonstration, if no part is found, return mock data
          if (barcode === "123456789") {
            return json({
              success: true,
              part: {
                id: "demo-part-1",
                name: "Compressor AC-2000",
                partNumber: "123456789",
                sku: "COMP-AC2000",
                description: "2-ton residential AC compressor",
                category: "Compressors",
                currentStock: 12,
                minimumStock: 5,
                reorderPoint: 8,
                unitOfMeasure: "unit",
                costPrice: 450.00,
                sellingPrice: 750.00,
                supplier: {
                  name: "CoolAir Supplies"
                },
                locations: [
                  { name: "Main Warehouse", quantity: 8 },
                  { name: "Service Truck #3", quantity: 4 }
                ]
              }
            });
          } else if (barcode === "987654321") {
            return json({
              success: true,
              part: {
                id: "demo-part-2",
                name: "Air Filter HEPA-500",
                partNumber: "987654321",
                sku: "FILT-HEPA500",
                description: "High-efficiency HEPA filter for residential systems",
                category: "Filters",
                currentStock: 35,
                minimumStock: 10,
                reorderPoint: 20,
                unitOfMeasure: "unit",
                costPrice: 15.00,
                sellingPrice: 29.95,
                supplier: {
                  name: "FilterPro Inc."
                },
                locations: [
                  { name: "Main Warehouse", quantity: 30 },
                  { name: "Showroom", quantity: 5 }
                ]
              }
            });
          } else {
            return json({ success: false, error: "Part not found" });
          }
        }

        // Transform data structure to match our expected format
        return json({
          success: true,
          part: {
            id: part.id,
            name: part.name,
            partNumber: part.partNumber,
            sku: part.sku,
            description: part.description,
            category: part.category,
            currentStock: part.currentStock,
            minimumStock: part.minimumStock,
            reorderPoint: part.reorderPoint,
            unitOfMeasure: part.unitOfMeasure,
            costPrice: part.costPrice,
            sellingPrice: part.sellingPrice,
            supplier: part.supplier ? {
              name: part.supplier.name
            } : null,
            locations: part.inventoryLocations.map(il => ({
              name: il.location.name,
              quantity: il.quantity
            }))
          }
        });
      } catch (error) {
        console.error("Error looking up barcode:", error);
        return json({ success: false, error: "Failed to look up barcode" });
      }
    }

    case "recordTransaction": {
      const partId = formData.get("partId") as string;
      const locationId = formData.get("locationId") as string;
      const quantity = Number(formData.get("quantity"));
      const transactionType = formData.get("transactionType") as string;
      const reason = formData.get("reason") as string;

      if (!partId || !locationId || !quantity || !transactionType) {
        return json({ success: false, error: "Missing required fields" });
      }

      try {
        // In a real implementation, this would create a transaction in the database
        // For demo purposes, we'll just return success
        return json({
          success: true,
          message: `Successfully recorded ${transactionType.toLowerCase()} of ${quantity} units`
        });
      } catch (error) {
        console.error("Error recording transaction:", error);
        return json({ success: false, error: "Failed to record transaction" });
      }
    }

    default:
      return json({ success: false, error: "Invalid action" });
  }
}

export default function InventoryScanner() {
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  const [barcode, setBarcode] = useState("");
  const [scanning, setScanning] = useState(false);
  const [hasCamera, setHasCamera] = useState(false);
  const [cameraError, setCameraError] = useState("");
  const [scannedPart, setScannedPart] = useState<any>(null);
  const [selectedLocation, setSelectedLocation] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [transactionType, setTransactionType] = useState("USAGE");
  const [reason, setReason] = useState("");
  const [transactionSuccess, setTransactionSuccess] = useState(false);

  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const barcodeInputRef = useRef<HTMLInputElement | null>(null);

  // Check if the browser supports the camera
  useEffect(() => {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      setHasCamera(true);
    } else {
      setHasCamera(false);
      setCameraError("Your browser doesn't support camera access. Please use a modern browser or enter barcodes manually.");
    }
  }, []);

  // Process action results
  useEffect(() => {
    if (actionData?.success) {
      if ('part' in actionData && actionData.part) {
        setScannedPart(actionData.part);

        // If part has locations, select the first one by default
        if (actionData.part.locations && actionData.part.locations.length > 0) {
          setSelectedLocation(actionData.part.locations[0].name);
        }
      }

      if ('message' in actionData && actionData.message) {
        setTransactionSuccess(true);
        // Reset form after successful transaction
        setTimeout(() => {
          setScannedPart(null);
          setBarcode("");
          setSelectedLocation("");
          setQuantity(1);
          setReason("");
          setTransactionSuccess(false);
          // Focus back on barcode input
          if (barcodeInputRef.current) {
            barcodeInputRef.current.focus();
          }
        }, 3000);
      }
    }
  }, [actionData]);

  // Start camera when scanning is enabled
  useEffect(() => {
    let stream: MediaStream | null = null;

    if (scanning && videoRef.current && hasCamera) {
      navigator.mediaDevices.getUserMedia({ video: { facingMode: "environment" } })
        .then(videoStream => {
          stream = videoStream;
          if (videoRef.current) {
            videoRef.current.srcObject = videoStream;
            videoRef.current.play();
          }
        })
        .catch(err => {
          console.error("Error accessing camera:", err);
          setCameraError(`Error accessing camera: ${err.message || 'Unknown error'}`);
          setScanning(false);
        });
    }

    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [scanning, hasCamera]);

  // Mock barcode scanning function - in a real app, would use a library like zxing or quagga
  const captureBarcode = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // In a real implementation, we would process the image to find barcodes
    // For this demo, we'll just simulate finding a random barcode
    const mockBarcodes = ["123456789", "987654321"];
    const randomBarcode = mockBarcodes[Math.floor(Math.random() * mockBarcodes.length)];

    // Stop scanning and lookup the barcode
    setScanning(false);
    setBarcode(randomBarcode);

    // Submit the form to look up the barcode
    const formData = new FormData();
    formData.append("action", "lookupBarcode");
    formData.append("barcode", randomBarcode);
    submit(formData, { method: "post" });
  };

  // Handle manual barcode submission
  const handleBarcodeSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!barcode.trim()) return;

    const formData = new FormData();
    formData.append("action", "lookupBarcode");
    formData.append("barcode", barcode.trim());
    submit(formData, { method: "post" });
  };

  // Handle transaction submission
  const handleTransactionSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!scannedPart || !selectedLocation || quantity <= 0) return;

    const formData = new FormData();
    formData.append("action", "recordTransaction");
    formData.append("partId", scannedPart.id);
    formData.append("locationId", selectedLocation);
    formData.append("quantity", quantity.toString());
    formData.append("transactionType", transactionType);
    formData.append("reason", reason);
    submit(formData, { method: "post" });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Inventory Scanner</h1>
        <div className="flex gap-2">
          <Link
            to="/inventory"
            className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <ArchiveBoxIcon className="h-4 w-4" />
            Inventory List
          </Link>
          <Link
            to="/inventory/transactions"
            className="flex items-center gap-1 rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            <ArrowDownTrayIcon className="h-4 w-4" />
            All Transactions
          </Link>
        </div>
      </div>

      <div className="rounded-lg bg-white p-6 shadow">
        <div className="mb-6 flex items-center">
          <QrCodeIcon className="mr-3 h-6 w-6 text-blue-500" />
          <h2 className="text-xl font-medium">Barcode Scanner</h2>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Camera Scanner */}
          <div>
            <h3 className="mb-4 text-lg font-medium">Scan with Camera</h3>

            {cameraError ? (
              <div className="rounded-md bg-yellow-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">{cameraError}</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="relative overflow-hidden rounded-lg bg-gray-100 shadow-inner">
                  {scanning ? (
                    <div className="relative aspect-video w-full overflow-hidden rounded-md bg-black">
                      <video
                        ref={videoRef}
                        className="h-full w-full object-cover"
                        autoPlay
                        playsInline
                        muted
                      />
                      <canvas ref={canvasRef} className="hidden" />

                      {/* Scanning indicator */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="h-1 w-full animate-[scanner_2s_linear_infinite] bg-red-500 opacity-50"></div>
                      </div>

                      {/* Controls overlay */}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                        <div className="flex justify-center">
                          <button
                            type="button"
                            onClick={captureBarcode}
                            className="flex items-center rounded-full bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-md hover:bg-gray-50"
                          >
                            <CameraIcon className="mr-2 h-5 w-5 text-gray-500" />
                            Capture
                          </button>
                          <button
                            type="button"
                            onClick={() => setScanning(false)}
                            className="ml-2 flex items-center rounded-full bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-md hover:bg-red-700"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex aspect-video w-full items-center justify-center rounded-md border-2 border-dashed border-gray-300 p-4">
                      <button
                        type="button"
                        onClick={() => setScanning(true)}
                        className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
                        disabled={!hasCamera}
                      >
                        <CameraIcon className="mr-2 h-5 w-5" />
                        Start Scanner
                      </button>
                    </div>
                  )}
                </div>

                <p className="text-sm text-gray-500">
                  Position the barcode within the camera view and press the Capture button,
                  or use the manual entry below.
                </p>
              </div>
            )}
          </div>

          {/* Manual Entry */}
          <div>
            <h3 className="mb-4 text-lg font-medium">Manual Entry</h3>

            <Form method="post" onSubmit={handleBarcodeSubmit}>
              <input type="hidden" name="action" value="lookupBarcode" />

              <div className="space-y-4">
                <div>
                  <label htmlFor="barcode" className="block text-sm font-medium text-gray-700">
                    Barcode / SKU / Part Number
                  </label>
                  <div className="mt-1 flex">
                    <input
                      type="text"
                      id="barcode"
                      name="barcode"
                      ref={barcodeInputRef}
                      value={barcode}
                      onChange={(e) => setBarcode(e.target.value)}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Enter barcode or part number"
                    />
                    <button
                      type="submit"
                      className="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Lookup
                    </button>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Try sample barcodes: 123456789 or 987654321
                  </p>
                </div>

                {actionData?.error && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800">{actionData.error}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Form>
          </div>
        </div>
      </div>

      {/* Part Details and Transaction */}
      {scannedPart && (
        <div className="space-y-6">
          {/* Part Details */}
          <div className="rounded-lg bg-white p-6 shadow">
            <h2 className="mb-4 text-xl font-medium">Part Details</h2>

            <div className="overflow-hidden bg-white shadow sm:rounded-lg">
              <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
                <dl className="sm:divide-y sm:divide-gray-200">
                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Part Name</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{scannedPart.name}</dd>
                  </div>
                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Part Number</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{scannedPart.partNumber}</dd>
                  </div>
                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">SKU</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{scannedPart.sku || 'N/A'}</dd>
                  </div>
                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Category</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{scannedPart.category || 'Uncategorized'}</dd>
                  </div>
                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Current Stock</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        scannedPart.currentStock <= scannedPart.minimumStock ? 'bg-red-100 text-red-800' :
                        scannedPart.currentStock <= scannedPart.reorderPoint ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {scannedPart.currentStock} {scannedPart.unitOfMeasure}
                      </span>
                    </dd>
                  </div>
                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Supplier</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{scannedPart.supplier?.name || 'No supplier'}</dd>
                  </div>
                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Locations</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                      <ul className="divide-y divide-gray-200 rounded-md border border-gray-200">
                        {scannedPart.locations && scannedPart.locations.map((loc, idx) => (
                          <li key={idx} className="flex items-center justify-between py-3 pl-3 pr-4 text-sm">
                            <div className="flex w-0 flex-1 items-center">
                              <MapPinIcon className="h-5 w-5 flex-shrink-0 text-gray-400" aria-hidden="true" />
                              <span className="ml-2 w-0 flex-1 truncate">{loc.name}</span>
                            </div>
                            <div className="ml-4 flex-shrink-0">
                              <span className="font-medium">{loc.quantity} {scannedPart.unitOfMeasure}</span>
                            </div>
                          </li>
                        ))}
                        {(!scannedPart.locations || scannedPart.locations.length === 0) && (
                          <li className="py-3 pl-3 pr-4 text-sm text-gray-500">No location data available</li>
                        )}
                      </ul>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>

          {/* Transaction Form */}
          <div className="rounded-lg bg-white p-6 shadow">
            <h2 className="mb-4 text-xl font-medium">Record Transaction</h2>

            {transactionSuccess ? (
              <div className="rounded-md bg-green-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800">{actionData?.message || "Transaction recorded successfully"}</p>
                  </div>
                </div>
              </div>
            ) : (
              <Form method="post" onSubmit={handleTransactionSubmit}>
                <input type="hidden" name="action" value="recordTransaction" />
                <input type="hidden" name="partId" value={scannedPart.id} />

                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  {/* Transaction Type */}
                  <div className="sm:col-span-3">
                    <label htmlFor="transactionType" className="block text-sm font-medium text-gray-700">
                      Transaction Type
                    </label>
                    <select
                      id="transactionType"
                      name="transactionType"
                      value={transactionType}
                      onChange={(e) => setTransactionType(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="USAGE">Remove Stock (Usage)</option>
                      <option value="RECEIPT">Add Stock (Receipt)</option>
                      <option value="ADJUSTMENT">Adjustment</option>
                    </select>
                  </div>

                  {/* Location */}
                  <div className="sm:col-span-3">
                    <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">
                      Location
                    </label>
                    <select
                      id="locationId"
                      name="locationId"
                      value={selectedLocation}
                      onChange={(e) => setSelectedLocation(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select location</option>
                      {scannedPart.locations && scannedPart.locations.map((loc, idx) => (
                        <option key={idx} value={loc.name}>{loc.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* Quantity */}
                  <div className="sm:col-span-2">
                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                      Quantity
                    </label>
                    <input
                      type="number"
                      id="quantity"
                      name="quantity"
                      value={quantity}
                      onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 0))}
                      min="1"
                      max={transactionType === "USAGE" ? scannedPart.currentStock : 999}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  {/* Reason */}
                  <div className="sm:col-span-4">
                    <label htmlFor="reason" className="block text-sm font-medium text-gray-700">
                      Reason / Reference
                    </label>
                    <input
                      type="text"
                      id="reason"
                      name="reason"
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Service order #, delivery note, etc."
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="sm:col-span-6">
                    <button
                      type="submit"
                      className="inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      disabled={!selectedLocation || quantity <= 0}
                    >
                      Record Transaction
                    </button>
                  </div>
                </div>
              </Form>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Utility Component for MapPinIcon used in the part details section
function MapPinIcon(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
    </svg>
  );
}