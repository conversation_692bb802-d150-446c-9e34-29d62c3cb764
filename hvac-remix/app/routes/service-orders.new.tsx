import * as React from "react";
import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation, Link, useSearchParams } from "@remix-run/react";
import { useEffect, useRef, useState } from "react";
import { requireUserId } from "~/session.server";
import { createServiceOrder } from "~/services/service-order.service";
import { getCustomers } from "~/services/customer.service";
import { getDevices } from "~/services/device.service";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { RichTextEditor, plateValueToString } from "~/components/ui/rich-text-editor";
import type { Value } from "@udecode/plate";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "~/components/ui/select";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  const preselectedCustomerId = url.searchParams.get("customerId");
  const preselectedDeviceId = url.searchParams.get("deviceId");

  // Get all customers for the dropdown
  const customersResponse = await getCustomers(userId);

  // Get devices for the selected customer if a customer is preselected
  let devicesResponse = { data: [] };
  if (preselectedCustomerId) {
    devicesResponse = await getDevices(
      userId,
      { pageSize: 100 },
      { customerId: preselectedCustomerId }
    );
  }

  return json({
    customers: customersResponse.data || [],
    devices: devicesResponse.data || [],
    preselectedCustomerId,
    preselectedDeviceId
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();

  const title = formData.get("title") as string;
  const description = formData.get("description") as string;
  const status = formData.get("status") as string;
  const priority = formData.get("priority") as string;
  const type = formData.get("type") as string;
  const scheduledDate = formData.get("scheduledDate") as string;
  const notesJson = formData.get("notes") as string;
  const customerId = formData.get("customerId") as string;
  const deviceId = formData.get("deviceId") as string;

  // Parse the rich text editor value for notes
  let notes: string | null = null;
  try {
    if (notesJson) {
      const notesValue = JSON.parse(notesJson) as Value;
      notes = plateValueToString(notesValue);
    }
  } catch (e) {
    console.error("Error parsing rich text content:", e);
    notes = notesJson;
  }

  // Validate required fields
  const errors = {
    title: title ? null : "Title is required",
    customerId: customerId ? null : "Customer is required",
    status: status ? null : "Status is required",
    priority: priority ? null : "Priority is required",
    type: type ? null : "Type is required",
  };

  const hasErrors = Object.values(errors).some(
    (errorMessage) => errorMessage !== null
  );

  if (hasErrors) {
    const values = {
      title: title || "",
      description: description || "",
      priority: priority || "",
      customerId: customerId || "",
      deviceId: deviceId || "",
      scheduledDate: scheduledDate || "",
      status: formData.get("status")?.toString() || "",
      type: formData.get("type")?.toString() || "",
    };
    return json({ errors, values });
  }

  // Create service order
  const serviceOrderResponse = await createServiceOrder(
    {
      title,
      description: description || null,
      status,
      priority,
      type,
      scheduledDate: scheduledDate ? new Date(scheduledDate) : null,
      completedDate: null,
      notes: notes || null,
      customerId,
      deviceId: deviceId || null,
    },
    userId
  );

  if (!serviceOrderResponse.success) {
    return json({
      errors: {
        ...errors,
        form: serviceOrderResponse.error || "Failed to create service order",
      },
      values: {
        title: title || "",
        description: description || "",
        priority: priority || "",
        customerId: customerId || "",
        deviceId: deviceId || "",
        scheduledDate: scheduledDate || "",
        status: formData.get("status")?.toString() || "",
        type: formData.get("type")?.toString() || "",
      },
    });
  }

  return redirect(`/service-orders/${serviceOrderResponse.data?.id}`);
}

export default function NewServiceOrderPage() {
  const { customers, devices, preselectedCustomerId, preselectedDeviceId } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  const [searchParams] = useSearchParams();

  // State for rich text editor
  const [notesValue, setNotesValue] = useState<Value>([
    { type: 'p', children: [{ text: '' }] },
  ]);

  const titleRef = useRef<HTMLInputElement>(null);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>(
    preselectedCustomerId || (actionData?.values?.customerId as string) || ""
  );
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>(
    preselectedDeviceId || (actionData?.values?.deviceId as string) || ""
  );
  const [customerDevices, setCustomerDevices] = useState(devices);

  // Focus on the title field when there's an error
  useEffect(() => {
    if (actionData?.errors?.title) {
      titleRef.current?.focus();
    }
  }, [actionData]);

  // Fetch devices when customer changes
  useEffect(() => {
    const fetchDevices = async () => {
      if (selectedCustomerId) {
        const response = await fetch(`/api/customers/${selectedCustomerId}/devices`);
        if (response.ok) {
          const data = await response.json();
          setCustomerDevices(data.devices);
          // Clear selected device if it doesn't belong to the new customer
          if (selectedDeviceId && !data.devices.some((device: any) => device.id === selectedDeviceId)) {
            setSelectedDeviceId("");
          }
        } else {
          setCustomerDevices([]);
          setSelectedDeviceId("");
        }
      } else {
        setCustomerDevices([]);
        setSelectedDeviceId("");
      }
    };

    // Use the devices from the loader if the customer is preselected
    if (selectedCustomerId === preselectedCustomerId && devices.length > 0) {
      setCustomerDevices(devices);
    } else if (selectedCustomerId) {
      fetchDevices();
    }
  }, [selectedCustomerId, preselectedCustomerId, devices, selectedDeviceId]);

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/service-orders" className="text-blue-500 hover:underline">
          ← Back to Service Orders
        </Link>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Create New Service Order</CardTitle>
          <CardDescription>
            Create a new service order record in the system
          </CardDescription>
        </CardHeader>

        <Form method="post">
          <CardContent className="space-y-4">
            {/* Form error message */}
            {actionData?.errors && 'form' in actionData.errors && actionData.errors.form && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {String(actionData.errors.form)}
              </div>
            )}

            {/* Customer field */}
            <div className="space-y-2">
              <Label htmlFor="customerId">
                Customer <span className="text-red-500">*</span>
              </Label>
              <Select
                name="customerId"
                defaultValue={selectedCustomerId}
                onValueChange={setSelectedCustomerId}
              >
                <SelectTrigger
                  id="customerId"
                  className={actionData?.errors?.customerId ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {actionData?.errors?.customerId && (
                <p className="text-red-500 text-sm" id="customerId-error">
                  {actionData.errors.customerId}
                </p>
              )}
            </div>

            {/* Device field */}
            <div className="space-y-2">
              <Label htmlFor="deviceId">Device</Label>
              <Select
                name="deviceId"
                defaultValue={selectedDeviceId}
                onValueChange={setSelectedDeviceId}
                disabled={!selectedCustomerId || customerDevices.length === 0}
              >
                <SelectTrigger id="deviceId">
                  <SelectValue placeholder={
                    !selectedCustomerId
                      ? "Select a customer first"
                      : customerDevices.length === 0
                        ? "No devices available"
                        : "Select a device"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {customerDevices.map((device) => (
                    <SelectItem key={device.id} value={device.id}>
                      {device.name} {device.model ? `(${device.model})` : ""}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Title field */}
            <div className="space-y-2">
              <Label htmlFor="title">
                Title <span className="text-red-500">*</span>
              </Label>
              <Input
                ref={titleRef}
                id="title"
                name="title"
                defaultValue={actionData?.values?.title as string}
                aria-invalid={actionData?.errors?.title ? true : undefined}
                aria-errormessage={
                  actionData?.errors?.title ? "title-error" : undefined
                }
              />
              {actionData?.errors?.title && (
                <p className="text-red-500 text-sm" id="title-error">
                  {actionData.errors.title}
                </p>
              )}
            </div>

            {/* Description field */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                rows={4}
                defaultValue={actionData?.values?.description as string}
              />
            </div>

            {/* Status field */}
            <div className="space-y-2">
              <Label htmlFor="status">
                Status <span className="text-red-500">*</span>
              </Label>
              <Select
                name="status"
                defaultValue={actionData?.values?.status as string || "PENDING"}
              >
                <SelectTrigger
                  id="status"
                  className={actionData?.errors?.status ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              {actionData?.errors?.status && (
                <p className="text-red-500 text-sm" id="status-error">
                  {actionData.errors.status}
                </p>
              )}
            </div>

            {/* Priority field */}
            <div className="space-y-2">
              <Label htmlFor="priority">
                Priority <span className="text-red-500">*</span>
              </Label>
              <Select
                name="priority"
                defaultValue={actionData?.values?.priority as string || "MEDIUM"}
              >
                <SelectTrigger
                  id="priority"
                  className={actionData?.errors?.priority ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">Low</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="URGENT">Urgent</SelectItem>
                </SelectContent>
              </Select>
              {actionData?.errors?.priority && (
                <p className="text-red-500 text-sm" id="priority-error">
                  {actionData.errors.priority}
                </p>
              )}
            </div>

            {/* Type field */}
            <div className="space-y-2">
              <Label htmlFor="type">
                Type <span className="text-red-500">*</span>
              </Label>
              <Select
                name="type"
                defaultValue={actionData?.values?.type as string || "SERVICE"}
              >
                <SelectTrigger
                  id="type"
                  className={actionData?.errors?.type ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SERVICE">Service</SelectItem>
                  <SelectItem value="INSTALLATION">Installation</SelectItem>
                  <SelectItem value="INSPECTION">Inspection</SelectItem>
                </SelectContent>
              </Select>
              {actionData?.errors?.type && (
                <p className="text-red-500 text-sm" id="type-error">
                  {actionData.errors.type}
                </p>
              )}
            </div>

            {/* Scheduled Date field */}
            <div className="space-y-2">
              <Label htmlFor="scheduledDate">Scheduled Date</Label>
              <Input
                id="scheduledDate"
                name="scheduledDate"
                type="date"
                defaultValue={actionData?.values?.scheduledDate as string}
              />
            </div>

            {/* Notes field */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <RichTextEditor
                name="notes"
                value={notesValue}
                onChange={setNotesValue}
                minHeight="200px"
              />
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Link to="/service-orders">
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Service Order"}
            </Button>
          </CardFooter>
        </Form>
      </Card>
    </div>
  );
}
