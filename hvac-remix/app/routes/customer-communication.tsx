import * as React from "react";
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData } from "@remix-run/react";
import { requireUser } from "~/session.server";
import { CustomerCommunicationCenter } from "~/components/organisms/customer-communication-center";
import { Card } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { useState } from "react";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const user = await requireUser(request);

  // Get customer ID from query params if available
  const url = new URL(request.url);
  const customerId = url.searchParams.get("customerId");

  // Przykładowe dane wiadomości - w przyszłości będą pobierane z bazy danych
  const messages = [
    {
      id: "msg1",
      customerId: "cust1",
      userId: "user1",
      direction: "OUTBOUND",
      channel: "EMAIL",
      content: "<PERSON><PERSON><PERSON> dobry,\n\nInformujemy, że Państwa zlecenie serwisowe zostało zaplanowane na 15.06.2023 w godzinach 10:00-12:00.\n\nZ poważaniem,\nZespół HVAC CRM",
      subject: "Potwierdzenie zlecenia serwisowego",
      timestamp: new Date(Date.now() - 2 * 86400000).toISOString(), // 2 days ago
      read: true
    },
    {
      id: "msg2",
      customerId: "cust1",
      userId: undefined,
      direction: "INBOUND",
      channel: "EMAIL",
      content: "Dzień dobry,\n\nDziękuję za informację. Czy możliwe jest przesunięcie terminu na godziny popołudniowe?\n\nPozdrawiam,\nJan Kowalski",
      subject: "Re: Potwierdzenie zlecenia serwisowego",
      timestamp: new Date(Date.now() - 1 * 86400000).toISOString(), // 1 day ago
      read: false
    },
    {
      id: "msg3",
      customerId: "cust1",
      userId: "user1",
      direction: "OUTBOUND",
      channel: "SMS",
      content: "Przypominamy o jutrzejszej wizycie serwisowej w godz. 14:00-16:00. Prosimy o potwierdzenie. Zespół HVAC CRM",
      timestamp: new Date(Date.now() - 12 * 3600000).toISOString(), // 12 hours ago
      read: true
    },
    {
      id: "msg4",
      customerId: "cust1",
      userId: undefined,
      direction: "INBOUND",
      channel: "SMS",
      content: "Potwierdzam wizytę. Dziękuję za przypomnienie.",
      timestamp: new Date(Date.now() - 10 * 3600000).toISOString(), // 10 hours ago
      read: false
    },
    {
      id: "msg5",
      customerId: "cust2",
      userId: "user1",
      direction: "OUTBOUND",
      channel: "EMAIL",
      content: "Szanowni Państwo,\n\nPrzesyłamy ofertę na wymianę klimatyzacji w Państwa biurze. Oferta jest ważna przez 30 dni.\n\nZ poważaniem,\nZespół HVAC CRM",
      subject: "Oferta na wymianę klimatyzacji",
      timestamp: new Date(Date.now() - 5 * 86400000).toISOString(), // 5 days ago
      read: true,
      attachments: [
        {
          id: "att1",
          name: "Oferta_XYZ_2023.pdf",
          url: "/attachments/Oferta_XYZ_2023.pdf",
          type: "application/pdf"
        }
      ]
    }
  ];

  // Filter messages by customer ID if provided
  const filteredMessages = customerId
    ? messages.filter(msg => msg.customerId === customerId)
    : messages;

  // Przykładowe dane klientów - w przyszłości będą pobierane z bazy danych
  const customers = [
    {
      id: "cust1",
      name: "Jan Kowalski",
      email: "<EMAIL>",
      phone: "123-456-789",
      address: "ul. Przykładowa 1, Warszawa"
    },
    {
      id: "cust2",
      name: "Firma XYZ Sp. z o.o.",
      email: "<EMAIL>",
      phone: "987-654-321",
      address: "ul. Biznesowa 10, Kraków"
    }
  ];

  // Get customer details if customer ID is provided
  const customerDetails = customerId
    ? customers.find(cust => cust.id === customerId)
    : null;

  return json({
    user,
    messages: filteredMessages,
    customers,
    customerDetails
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const user = await requireUser(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "send-message") {
    const customerId = formData.get("customerId") as string;
    const channel = formData.get("channel") as string;
    const subject = formData.get("subject") as string;
    const content = formData.get("content") as string;

    // W przyszłości: zapis wiadomości do bazy danych

    return json({ success: true, message: "Wiadomość została wysłana" });
  }

  if (action === "mark-as-read") {
    const messageId = formData.get("messageId") as string;

    // W przyszłości: aktualizacja statusu wiadomości w bazie danych

    return json({ success: true, message: "Wiadomość oznaczona jako przeczytana" });
  }

  return json({ success: false, message: "Nieznana akcja" });
};

export default function CustomerCommunicationPage() {
  const { user, messages, customers, customerDetails } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(
    customerDetails ? customerDetails.id : null
  );

  // Filter messages by selected customer
  const filteredMessages = selectedCustomerId
    ? messages.filter(msg => msg.customerId === selectedCustomerId)
    : messages;

  // Handle sending a message
  const handleSendMessage = async (message: {
    customerId: string;
    channel: string;
    subject?: string;
    content: string;
  }) => {
    const formData = new FormData();
    formData.append("action", "send-message");
    formData.append("customerId", message.customerId);
    formData.append("channel", message.channel);
    if (message.subject) formData.append("subject", message.subject);
    formData.append("content", message.content);

    // W przyszłości: wysłanie formularza za pomocą fetch lub useSubmit

    // Symulacja sukcesu
    return Promise.resolve();
  };

  // Handle marking a message as read
  const handleMarkAsRead = async (messageId: string) => {
    const formData = new FormData();
    formData.append("action", "mark-as-read");
    formData.append("messageId", messageId);

    // W przyszłości: wysłanie formularza za pomocą fetch lub useSubmit

    // Symulacja sukcesu
    return Promise.resolve();
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Komunikacja z klientami</h1>

      {/* Customer selector */}
      {!customerDetails && (
        <Card className="p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">Wybierz klienta</h2>
          <div className="flex flex-col md:flex-row gap-4">
            <Input
              type="text"
              placeholder="Wyszukaj klienta..."
              className="md:w-1/3"
            />
            <div className="flex flex-wrap gap-2">
              {customers.map((customer) => (
                <Button
                  key={customer.id}
                  variant={selectedCustomerId === customer.id ? "default" : "outline"}
                  onClick={() => setSelectedCustomerId(customer.id)}
                >
                  {customer.name}
                </Button>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* Communication center */}
      {selectedCustomerId && (
        <CustomerCommunicationCenter
          customerId={selectedCustomerId}
          customerName={
            customers.find(c => c.id === selectedCustomerId)?.name || "Klient"
          }
          messages={filteredMessages}
          onSendMessage={handleSendMessage}
          onMarkAsRead={handleMarkAsRead}
        />
      )}
    </div>
  );
}
